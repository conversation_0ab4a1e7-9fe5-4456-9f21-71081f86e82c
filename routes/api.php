<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\AuthenticationController;
use App\Http\Controllers\DetourController;
use App\Http\Controllers\GTFSController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:api');

/*
    Authentication
*/

Route::post('oauth',  [AuthenticationController::class, 'index']);   
Route::delete('sign-out',  [AuthenticationController::class, 'revoke']);   
Route::get('oauth/status',  [AuthenticationController::class, 'get']);

Route::get('/detour-summary', [DetourController::class, 'getDetoursData']);

Route::get('/detour-card-data', [DetourController::class, 'getDetoursCardData']);

Route::get('/intersection-aws-location', [DetourController::class, 'getIntersectionFromAWSLocation']);

Route::post('/gtfs-upload',  [GTFSController::class, 'upload']);