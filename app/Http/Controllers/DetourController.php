<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class DetourController extends Controller
{
    public function getDetoursData()
    {
        $redisKeyPattern = 'detour:parsed:*';
        
        $detourKeys = Redis::keys($redisKeyPattern);

        if (!empty($detourKeys)) {
            $detoursData = [];

            foreach ($detourKeys as $key) {
                $detour = Redis::get($key);
                if ($detour) {
                    $detoursData[] = json_decode($detour, true);
                }
            }
            return response()->json($detoursData, 200);
        } else {
            return response()->json(['error' => 'No detour data found in Redis'], 404);
        }
    }

    public function getDetoursCardData()
    {
        $redisKey = 'sql:card:detours';
        
        $detoursCardData = [];

        $detourCardData = Redis::get($redisKey);

        if ($detourCardData) {
            $detoursCardData = json_decode($detourCardData, true);
        } else {
            return response()->json(['error' => 'No detour CARD data found in Redis'], 404);
        }

        return response()->json($detoursCardData, 200);
    }

    public function getIntersectionFromAWSLocation(Request $request)
    {
        $latitude = $request->input('lat');
        $longitude = $request->input('lon');

        if (!$latitude || !$longitude) {
            return response()->json(['error' => 'Latitude and Longitude are required'], 400);
        }

        $client = new \Aws\LocationService\LocationServiceClient([
            'version' => 'latest',
            'region' => env('AWS_DEFAULT_REGION'), // Replace with your region
            'credentials' => [
                'key'    => env('AWS_ACCESS_KEY_ID'),
                'secret' => env('AWS_SECRET_ACCESS_KEY'),
            ],
        ]);

        $indexName = 'PhiladelphiaPlaceIndex';

        try {
            $response = $client->searchPlaceIndexForPosition([
                'IndexName' => $indexName,
                'Position' => [(float) $longitude, (float) $latitude],
                'MaxResults' => 10,
            ]);

            $intersectionName = [];
            foreach ($response['Results'] as $result) {
                if ($result['Distance'] < 50) {
                    $place = $result['Place'];
                    if (in_array('StreetType', $place['Categories']) || in_array('AddressType', $place['Categories'])) {
                        if (!in_array($place['Street'], $intersectionName)) {
                            $intersectionName[] = $place['Street'];
                        }
                    }
                }
            }

            $intersection = implode(' & ', array_slice($intersectionName, 0, 2));
            return response()->json(['intersection' => $intersection], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
