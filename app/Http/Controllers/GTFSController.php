<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class GTFSController extends Controller {

    public function upload(Request $request) {
        $request->validate([
            'file' => 'required|file',
            'file_name' => 'required|string',
            'release_date' => 'required|string'
        ]);

        try {

            $file = $request->file('file');
            $fileName = $request->input('file_name');

            $dateString = $request->input('release_date');
            $dateObject = Carbon::createFromFormat('m/d/Y', $dateString);

            $releaseDate = $dateObject->format('Y/m/d');
            $folderPath = "GTFS/{$releaseDate}/";

            $filePath = $folderPath . $fileName;

            $s3Disk = Storage::disk('s3');
            $s3Disk->put($filePath, file_get_contents($file), 'private');

            return response()->json([
                'message' => 'File uploaded successfully',
                'file_path' => $filePath,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error uploading file to S3: ' . $e->getMessage());

            return response()->json([
                'error' => 'Failed to upload file to S3',
                'details' => $e->getMessage(),
            ], 500);
        }
    }
}