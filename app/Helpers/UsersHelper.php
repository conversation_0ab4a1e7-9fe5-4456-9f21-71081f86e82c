<?php

namespace App\Helpers;

use <PERSON><PERSON>\Passport\Token;

class UsersHelper
{

    /*
     * access_level =   0 -> no access
     *                  1 -> admin access -> add/remove/modify users
     *                  2 -> ccim access -> add/remove alerts, advisories, etc.
     *                  3 -> trip cancellations -> add/modify trip cancellations.
     *                  99 -> developer access -> create routes, modes, etc. 
     */

    public $user_admins = [1, 99, 3];
    public $user_send = [1, 2, 99];
    public $user_editors = [1, 2, 99];
    public $user_trip_cancellations = [2, 3, 99];
    public $user_switches = [1, 2, 99];
    public $user_developer = [99];

    public function lookup($access_token)
    {
        $auth_header = explode(' ', $access_token);
        $token = $auth_header[1];
        $token_parts = explode('.', $token);
        $token_header = $token_parts[1];
        $token_header_json = base64_decode($token_header);
        $token_header_array = json_decode($token_header_json, true);
        $token_id = $token_header_array['jti'];

        return Token::find($token_id)->user;
    }

    public function token_status($access_token)
    {
        $auth_header = explode(' ', $access_token);
        $token = $auth_header[1];
        $token_parts = explode('.', $token);
        $token_header = $token_parts[1];
        $token_header_json = base64_decode($token_header);
        $token_header_array = json_decode($token_header_json, true);
        $token_id = $token_header_array['jti'];

        return Token::find($token_id)->user;
    }

    public function get_access($access_level)
    {
        if ($access_level == 0) {
            return "Reader";
        }

        if ($access_level == 1) {
            return "Administrator";
        }

        if ($access_level == 2) {
            return "Editor";
        }

        if ($access_level == 3) {
            return "Dispatcher";
        }

        if ($access_level == 99) {
            return "Developer";
        }

        return "Unknown";
    }
}
