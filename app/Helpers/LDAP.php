<?php

namespace App\Helpers;

use App\Exceptions\LdapException;
use Exception;
use Throwable;

class LDAP
{

    public $conn;
    public $config;

    public $host = 'ldap.septaad1.org';
    public $dn = 'DC=septaad1,DC=org';
    public $port = '389';

    public $domain = 'septaad1.org';
    public $mail_domain = 'septa.org';
    public $username = '';
    public $password = '';

    public $fields = array("name", "mail", "givenname", "sn", "mailnickname");

    public function __construct()
    {

        $this->config = [
            'host' => $this->host,
            'dn' => $this->dn,
            'domain' => $this->domain,
            'port' => $this->port,
        ];

        $this->host = env('LDAP_HOST', 'host');
        $this->dn = env('LDAP_DN', 'dc=domain,dc=com');
        $this->domain = env('LDAP_DOMAIN', 'septaad1.org');
        $this->port = env('LDAP_PORT', '389');
    }

    public function auth($username, $password)
    {

        if(str_contains($username, '@')){
            $username = strstr($username, '@', true); 
        }

        $this->conn = ldap_connect($this->host);
        ldap_set_option($this->conn, LDAP_OPT_PROTOCOL_VERSION, 3);
        ldap_set_option($this->conn, LDAP_OPT_REFERRALS, 0);

        try {
            if (ldap_bind($this->conn, $username . '@' . $this->domain, html_entity_decode($password))) {
                $bind = ldap_bind($this->conn, $username . '@' . $this->domain, html_entity_decode($password));
            } else {
                ldap_get_option($this->conn, LDAP_OPT_DIAGNOSTIC_MESSAGE, $err);
                throw new Exception(ldap_error($this->conn), $err);
            }
        } catch (Throwable $e) {
            return [
                "error" => true,
                "message" => "Autentication Failed."
            ];
        }

        if ($bind) {
            return $this->entry($username, $this->dn);
        }

        return false;
    }

    public function entry($username, $dn)
    {

        $conn = $this->conn;

        $filter = "(mail=$username@$this->mail_domain)";

        $results = ldap_search($conn, $this->dn, $filter);

        $search = [];

        if ($results) {
            $entries = ldap_get_entries($conn, $results);

            if ($entries['count'] > 0) {
                for ($i = 0; $i < $entries['count']; $i++) {
                    foreach ($this->fields as $field) {
                        $search[$field] = array_key_exists($field, $entries[$i]) ? $entries[$i][$field][0] : '';
                    }
                }
            }
        }

        $result = [];

        if ($search) {
            $result = array(
                "Name" => $search['name'],
                "FirstName" => $search['givenname'],
                "LastName" => $search['sn'],
                "UserID" => $search['mailnickname'],
                "Mail" => $search['mail'],
            );
        }

        ldap_unbind($conn);
        return $result;
    }
}
